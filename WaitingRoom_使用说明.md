# UNO 等待房间场景使用说明

## 概述

本文档介绍了为 Cocos Creator 3.x 项目创建的"玩家等待准备"竖屏界面的实现和使用方法。

## 文件结构

### 1. 数据模型
- **文件**: `assets/scripts/bean/PlayerInfo.ts`
- **功能**: 定义玩家信息接口和状态枚举

```typescript
export interface PlayerInfo {
    id: string;           // 玩家唯一ID
    name: string;         // 玩家昵称
    isReady: boolean;     // 是否已准备
    avatar?: string;      // 头像URL（可选）
    isCurrentPlayer?: boolean; // 是否为当前玩家
}
```

### 2. 玩家条目控制器
- **文件**: `assets/scripts/game/PlayerItemController.ts`
- **功能**: 管理单个玩家条目的显示和状态更新
- **主要方法**:
  - `updatePlayerInfo(playerInfo: PlayerInfo)`: 更新玩家信息显示
  - `setJoiningStatus()`: 设置加入中状态

### 3. 等待房间管理器
- **文件**: `assets/scripts/game/WaitingRoomManager.ts`
- **功能**: 管理整个等待房间的UI和逻辑
- **主要方法**:
  - `updatePlayerList(players: PlayerInfo[])`: 更新玩家列表
  - `onReadyButtonClick()`: 处理准备按钮点击
  - `onBackButtonClick()`: 处理返回按钮点击

### 4. 测试脚本
- **文件**: `assets/scripts/game/WaitingRoomTest.ts`
- **功能**: 简化版测试脚本，用于演示基本功能

## 场景结构

### 主要节点层次
```
WaitingRoom (Scene)
└── Canvas
    ├── Camera
    ├── TopPanel
    │   ├── BackButton (Button)
    │   └── TitleLabel (Label)
    ├── StatusLabel (Label)
    ├── PlayerListScrollView (ScrollView)
    │   └── view
    │       └── content
    │           └── PlayerItem (Prefab Instance)
    │               ├── Avatar (Sprite)
    │               ├── PlayerName (Label)
    │               ├── ReadyIcon (Sprite)
    │               └── ReadyStatus (Label)
    └── ReadyButton (Button)
        └── Label
```

## 玩家条目预制体结构

### PlayerItem.prefab 包含以下节点：
1. **Avatar**: 头像显示 (Sprite)
   - 位置: (-200, 0)
   - 大小: 80x80

2. **PlayerName**: 玩家昵称 (Label)
   - 位置: (-50, 15)
   - 显示玩家名称

3. **ReadyIcon**: 准备状态图标 (Sprite)
   - 位置: (150, 0)
   - 大小: 40x40
   - 显示勾号或问号图标

4. **ReadyStatus**: 准备状态文本 (Label)
   - 位置: (-50, -15)
   - 显示"已准备"、"未准备"或"加入中..."

## 在编辑器中的设置步骤

### 1. 添加脚本组件
1. 选择 Canvas 节点
2. 在 Inspector 面板中点击"添加组件"
3. 选择"自定义脚本" -> "WaitingRoomManager"

### 2. 设置组件引用
在 WaitingRoomManager 组件中设置以下引用：

- **backButton**: 拖拽 TopPanel/BackButton 节点
- **titleLabel**: 拖拽 TopPanel/TitleLabel 节点
- **statusLabel**: 拖拽 StatusLabel 节点
- **playerListScrollView**: 拖拽 PlayerListScrollView 节点
- **playerListContent**: 拖拽 PlayerListScrollView/view/content 节点
- **readyButton**: 拖拽 ReadyButton 节点
- **readyButtonLabel**: 拖拽 ReadyButton/Label 节点
- **playerItemPrefab**: 拖拽 PlayerItem.prefab 预制体

### 3. 设置玩家条目预制体
1. 选择 PlayerItem 预制体中的根节点
2. 添加 PlayerItemController 组件
3. 设置组件引用：
   - **avatarNode**: Avatar 节点
   - **playerNameLabel**: PlayerName 节点的 Label 组件
   - **readyIconNode**: ReadyIcon 节点
   - **readyStatusLabel**: ReadyStatus 节点的 Label 组件

## 使用方法

### 1. 基本使用
```typescript
// 获取等待房间管理器
const waitingRoomManager = this.getComponent(WaitingRoomManager);

// 更新玩家列表
const players: PlayerInfo[] = [
    { id: "1", name: "玩家1", isReady: false },
    { id: "2", name: "玩家2", isReady: true }
];
waitingRoomManager.updatePlayerList(players);

// 添加新玩家
waitingRoomManager.addPlayer({ id: "3", name: "玩家3", isReady: false });

// 更新玩家状态
waitingRoomManager.updatePlayerReadyState("1", true);
```

### 2. 事件处理
- **准备按钮**: 自动处理当前玩家的准备状态切换
- **返回按钮**: 可以在 `onBackButtonClick` 方法中添加场景切换逻辑

### 3. 网络集成
在实际项目中，您需要：
1. 在 `onReadyButtonClick` 中发送网络消息
2. 监听服务器消息来更新其他玩家状态
3. 处理玩家加入/离开事件

## 自定义和扩展

### 1. 添加头像支持
在 PlayerItemController 中的 `_updateAvatar` 方法中添加资源加载逻辑：

```typescript
private _updateAvatar(avatarPath?: string) {
    if (!avatarPath || !this.avatarNode) return;
    
    // 加载头像资源
    resources.load(avatarPath, SpriteFrame, (err, spriteFrame) => {
        if (!err && spriteFrame) {
            const sprite = this.avatarNode.getComponent(Sprite);
            if (sprite) {
                sprite.spriteFrame = spriteFrame;
            }
        }
    });
}
```

### 2. 添加动画效果
可以为状态变化添加动画效果，如缩放、颜色渐变等。

### 3. 添加音效
在按钮点击和状态变化时播放相应的音效。

## 注意事项

1. **预制体引用**: 确保 PlayerItem.prefab 正确设置了 PlayerItemController 组件
2. **节点路径**: 如果修改了节点层次结构，需要更新脚本中的路径查找逻辑
3. **资源管理**: 动态创建的玩家条目需要在适当时机销毁以避免内存泄漏
4. **网络同步**: 在多人游戏中，确保所有状态变化都正确同步到服务器

## 测试

使用 WaitingRoomTest.ts 脚本可以快速测试基本功能：
1. 将脚本添加到 Canvas 节点
2. 运行场景
3. 点击准备按钮测试状态切换
4. 在控制台查看日志输出

这个等待房间系统为您的 UNO 游戏提供了完整的玩家准备管理功能，可以根据具体需求进行进一步的定制和扩展。
