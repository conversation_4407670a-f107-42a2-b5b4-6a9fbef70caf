/**
 * 玩家信息数据模型
 * 用于等待房间中的玩家状态管理
 */
export interface PlayerInfo {
    /** 玩家唯一ID */
    id: string;
    
    /** 玩家昵称 */
    name: string;
    
    /** 是否已准备 */
    isReady: boolean;
    
    /** 头像URL或资源路径（可选） */
    avatar?: string;
    
    /** 是否为当前玩家 */
    isCurrentPlayer?: boolean;
}

/**
 * 玩家状态枚举
 */
export enum PlayerStatus {
    /** 未准备 */
    NOT_READY = 0,
    
    /** 已准备 */
    READY = 1,
    
    /** 正在加入 */
    JOINING = 2
}
