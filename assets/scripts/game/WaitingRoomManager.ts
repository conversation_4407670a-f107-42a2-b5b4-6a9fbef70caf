import { _decorator, Component, Node, Label, Button, ScrollView, Prefab, instantiate, Color, director } from 'cc';
import { PlayerInfo } from '../bean/PlayerInfo';
import { PlayerItemController } from './PlayerItemController';

const { ccclass, property } = _decorator;

/**
 * 等待房间管理器
 * 负责管理等待房间的UI显示和玩家状态
 */
@ccclass('WaitingRoomManager')
export class WaitingRoomManager extends Component {
    
    // UI 节点引用
    @property(Button)
    backButton: Button = null!;
    
    @property(Label)
    titleLabel: Label = null!;
    
    @property(Label)
    statusLabel: Label = null!;
    
    @property(ScrollView)
    playerListScrollView: ScrollView = null!;
    
    @property(Node)
    playerListContent: Node = null!;
    
    @property(Button)
    readyButton: Button = null!;
    
    @property(Label)
    readyButtonLabel: Label = null!;
    
    // 预制体引用
    @property(Prefab)
    playerItemPrefab: Prefab = null!;
    
    // 私有属性
    private _players: PlayerInfo[] = [];
    private _currentPlayerId: string = "player1"; // 当前玩家ID
    private _isCurrentPlayerReady: boolean = false;
    private _playerItemNodes: Node[] = [];
    
    onLoad() {
        this._initComponents();
        this._initEvents();
        this._initMockData();
    }
    
    start() {
        this._updatePlayerList(this._players);
        this._updateReadyButton(this._isCurrentPlayerReady);
    }
    
    /**
     * 初始化组件引用
     */
    private _initComponents() {
        // 如果没有在编辑器中设置，尝试自动查找
        if (!this.backButton) {
            const backNode = this.node.getChildByPath('TopPanel/BackButton');
            if (backNode) {
                this.backButton = backNode.getComponent(Button)!;
            }
        }
        
        if (!this.titleLabel) {
            const titleNode = this.node.getChildByPath('TopPanel/TitleLabel');
            if (titleNode) {
                this.titleLabel = titleNode.getComponent(Label)!;
            }
        }
        
        if (!this.statusLabel) {
            const statusNode = this.node.getChildByName('StatusLabel');
            if (statusNode) {
                this.statusLabel = statusNode.getComponent(Label)!;
            }
        }
        
        if (!this.playerListScrollView) {
            const scrollNode = this.node.getChildByName('PlayerListScrollView');
            if (scrollNode) {
                this.playerListScrollView = scrollNode.getComponent(ScrollView)!;
            }
        }
        
        if (!this.playerListContent) {
            this.playerListContent = this.node.getChildByPath('PlayerListScrollView/view/content')!;
        }
        
        if (!this.readyButton) {
            const readyNode = this.node.getChildByName('ReadyButton');
            if (readyNode) {
                this.readyButton = readyNode.getComponent(Button)!;
            }
        }
        
        if (!this.readyButtonLabel) {
            const labelNode = this.node.getChildByPath('ReadyButton/Label');
            if (labelNode) {
                this.readyButtonLabel = labelNode.getComponent(Label)!;
            }
        }
        
        // 设置标题
        if (this.titleLabel) {
            this.titleLabel.string = "等待房间";
        }
    }
    
    /**
     * 初始化事件监听
     */
    private _initEvents() {
        // 返回按钮事件
        if (this.backButton) {
            this.backButton.node.on(Button.EventType.CLICK, this.onBackButtonClick, this);
        }
        
        // 准备按钮事件
        if (this.readyButton) {
            this.readyButton.node.on(Button.EventType.CLICK, this.onReadyButtonClick, this);
        }
    }
    
    /**
     * 初始化模拟数据
     */
    private _initMockData() {
        this._players = [
            {
                id: "player1",
                name: "师家 A",
                isReady: false,
                isCurrentPlayer: true
            },
            {
                id: "player2", 
                name: "师家 B",
                isReady: false
            },
            {
                id: "player3",
                name: "师家 C", 
                isReady: true
            },
            {
                id: "player4",
                name: "师家 D",
                isReady: false
            }
        ];
    }
    
    /**
     * 更新玩家列表显示
     * @param players 玩家信息数组
     */
    public updatePlayerList(players: PlayerInfo[]) {
        this._players = players;
        this._updatePlayerList(players);
    }
    
    /**
     * 内部更新玩家列表方法
     */
    private _updatePlayerList(players: PlayerInfo[]) {
        // 清除现有的玩家条目
        this._clearPlayerItems();

        // 创建新的玩家条目
        for (const player of players) {
            this._createPlayerItem(player);
        }

        // 更新状态文本
        this._updateStatusText();
    }

    /**
     * 清除所有玩家条目
     */
    private _clearPlayerItems() {
        for (const itemNode of this._playerItemNodes) {
            if (itemNode && itemNode.isValid) {
                itemNode.destroy();
            }
        }
        this._playerItemNodes = [];
    }

    /**
     * 创建单个玩家条目
     * @param playerInfo 玩家信息
     */
    private _createPlayerItem(playerInfo: PlayerInfo) {
        if (!this.playerItemPrefab || !this.playerListContent) {
            console.error('PlayerItemPrefab 或 PlayerListContent 未设置');
            return;
        }

        // 实例化玩家条目预制体
        const itemNode = instantiate(this.playerItemPrefab);

        // 获取玩家条目控制器
        const controller = itemNode.getComponent(PlayerItemController);
        if (controller) {
            controller.updatePlayerInfo(playerInfo);
        }

        // 添加到内容节点
        this.playerListContent.addChild(itemNode);
        this._playerItemNodes.push(itemNode);
    }

    /**
     * 更新状态文本
     */
    private _updateStatusText() {
        if (!this.statusLabel) return;

        const readyCount = this._players.filter(p => p.isReady).length;
        const totalCount = this._players.length;

        this.statusLabel.string = `等待所有玩家准备 (${readyCount}/${totalCount})`;
    }

    /**
     * 更新准备按钮显示
     * @param isReady 是否已准备
     */
    private _updateReadyButton(isReady: boolean) {
        if (!this.readyButton || !this.readyButtonLabel) return;

        if (isReady) {
            this.readyButtonLabel.string = "取消准备";
            this.readyButtonLabel.color = new Color(255, 100, 100); // 红色
        } else {
            this.readyButtonLabel.string = "准备";
            this.readyButtonLabel.color = new Color(100, 255, 100); // 绿色
        }
    }

    /**
     * 更新当前玩家状态
     * @param isReady 是否已准备
     */
    private _updateLocalPlayerState(isReady: boolean) {
        this._isCurrentPlayerReady = isReady;

        // 更新玩家数据中的状态
        const currentPlayer = this._players.find(p => p.id === this._currentPlayerId);
        if (currentPlayer) {
            currentPlayer.isReady = isReady;
        }

        // 重新更新玩家列表显示
        this._updatePlayerList(this._players);

        // 更新准备按钮
        this._updateReadyButton(isReady);
    }

    /**
     * 准备按钮点击事件
     */
    public onReadyButtonClick() {
        const newReadyState = !this._isCurrentPlayerReady;
        this._updateLocalPlayerState(newReadyState);

        console.log(`玩家 ${this._currentPlayerId} ${newReadyState ? '已准备' : '取消准备'}`);

        // 这里可以发送网络消息通知服务器
        // this._sendReadyStateToServer(newReadyState);
    }

    /**
     * 返回按钮点击事件
     */
    public onBackButtonClick() {
        console.log('返回上一个场景');

        // 这里可以加载上一个场景
        // director.loadScene('MainMenu');
    }

    /**
     * 检查是否所有玩家都已准备
     */
    public checkAllPlayersReady(): boolean {
        return this._players.every(p => p.isReady);
    }

    /**
     * 添加新玩家
     * @param playerInfo 新玩家信息
     */
    public addPlayer(playerInfo: PlayerInfo) {
        this._players.push(playerInfo);
        this._updatePlayerList(this._players);
    }

    /**
     * 移除玩家
     * @param playerId 玩家ID
     */
    public removePlayer(playerId: string) {
        this._players = this._players.filter(p => p.id !== playerId);
        this._updatePlayerList(this._players);
    }

    /**
     * 更新指定玩家的准备状态
     * @param playerId 玩家ID
     * @param isReady 是否已准备
     */
    public updatePlayerReadyState(playerId: string, isReady: boolean) {
        const player = this._players.find(p => p.id === playerId);
        if (player) {
            player.isReady = isReady;
            this._updatePlayerList(this._players);
        }
    }
}
