import { _decorator, Component, Node, Label, <PERSON><PERSON>, Color, director, log } from 'cc';

const { ccclass, property } = _decorator;

/**
 * 等待房间演示脚本
 * 这是一个简化的演示版本，展示等待房间的基本功能
 */
@ccclass('WaitingRoomDemo')
export class WaitingRoomDemo extends Component {
    
    @property(Label)
    titleLabel: Label = null!;
    
    @property(Label)
    statusLabel: Label = null!;
    
    @property(Button)
    readyButton: Button = null!;
    
    @property(Label)
    readyButtonLabel: Label = null!;
    
    @property(Button)
    backButton: Button = null!;
    
    // 模拟玩家数据
    private _players = [
        { id: "player1", name: "师家 A", isReady: false, isCurrentPlayer: true },
        { id: "player2", name: "师家 B", isReady: false, isCurrentPlayer: false },
        { id: "player3", name: "师家 C", isReady: true, isCurrentPlayer: false },
        { id: "player4", name: "师家 D", isReady: false, isCurrentPlayer: false }
    ];
    
    private _currentPlayerId = "player1";
    private _isCurrentPlayerReady = false;
    
    onLoad() {
        this._findComponents();
        this._setupEvents();
        this._updateUI();
    }
    
    /**
     * 查找组件引用
     */
    private _findComponents() {
        // 查找标题标签
        if (!this.titleLabel) {
            const titleNode = this.node.getChildByPath('TopPanel/TitleLabel');
            if (titleNode) {
                this.titleLabel = titleNode.getComponent(Label)!;
            }
        }
        
        // 查找状态标签
        if (!this.statusLabel) {
            const statusNode = this.node.getChildByName('StatusLabel');
            if (statusNode) {
                this.statusLabel = statusNode.getComponent(Label)!;
            }
        }
        
        // 查找准备按钮
        if (!this.readyButton) {
            const readyNode = this.node.getChildByName('ReadyButton');
            if (readyNode) {
                this.readyButton = readyNode.getComponent(Button)!;
            }
        }
        
        // 查找准备按钮标签
        if (!this.readyButtonLabel) {
            const labelNode = this.node.getChildByPath('ReadyButton/Label');
            if (labelNode) {
                this.readyButtonLabel = labelNode.getComponent(Label)!;
            }
        }
        
        // 查找返回按钮
        if (!this.backButton) {
            const backNode = this.node.getChildByPath('TopPanel/BackButton');
            if (backNode) {
                this.backButton = backNode.getComponent(Button)!;
            }
        }
    }
    
    /**
     * 设置事件监听
     */
    private _setupEvents() {
        if (this.readyButton) {
            this.readyButton.node.on(Button.EventType.CLICK, this._onReadyButtonClick, this);
        }
        
        if (this.backButton) {
            this.backButton.node.on(Button.EventType.CLICK, this._onBackButtonClick, this);
        }
    }
    
    /**
     * 更新UI显示
     */
    private _updateUI() {
        // 设置标题
        if (this.titleLabel) {
            this.titleLabel.string = "等待房间";
        }
        
        // 更新状态文本
        this._updateStatusText();
        
        // 更新准备按钮
        this._updateReadyButton();
    }
    
    /**
     * 更新状态文本
     */
    private _updateStatusText() {
        if (!this.statusLabel) return;
        
        const readyCount = this._players.filter(p => p.isReady).length;
        const totalCount = this._players.length;
        
        this.statusLabel.string = `等待所有玩家准备 (${readyCount}/${totalCount})`;
        
        // 如果所有玩家都准备好了，显示特殊提示
        if (readyCount === totalCount && totalCount > 0) {
            this.statusLabel.string = "所有玩家已准备，即将开始游戏！";
            this.statusLabel.color = new Color(0, 255, 0); // 绿色
        } else {
            this.statusLabel.color = Color.WHITE;
        }
    }
    
    /**
     * 更新准备按钮
     */
    private _updateReadyButton() {
        if (!this.readyButton || !this.readyButtonLabel) return;
        
        if (this._isCurrentPlayerReady) {
            this.readyButtonLabel.string = "取消准备";
            this.readyButtonLabel.color = new Color(255, 100, 100); // 红色
        } else {
            this.readyButtonLabel.string = "准备";
            this.readyButtonLabel.color = new Color(100, 255, 100); // 绿色
        }
    }
    
    /**
     * 准备按钮点击事件
     */
    private _onReadyButtonClick() {
        this._isCurrentPlayerReady = !this._isCurrentPlayerReady;
        
        // 更新当前玩家状态
        const currentPlayer = this._players.find(p => p.id === this._currentPlayerId);
        if (currentPlayer) {
            currentPlayer.isReady = this._isCurrentPlayerReady;
        }
        
        // 更新UI
        this._updateUI();
        
        // 输出日志
        log(`玩家 ${this._currentPlayerId} ${this._isCurrentPlayerReady ? '已准备' : '取消准备'}`);
        
        // 检查是否所有玩家都已准备
        if (this._checkAllPlayersReady()) {
            log('所有玩家都已准备，可以开始游戏！');
            this._scheduleFunction(() => {
                log('3秒后自动开始游戏...');
                // 这里可以加载游戏场景
                // director.loadScene('GameScene');
            }, 3);
        }
    }
    
    /**
     * 返回按钮点击事件
     */
    private _onBackButtonClick() {
        log('返回上一个场景');
        // 这里可以加载主菜单场景
        // director.loadScene('MainMenu');
    }
    
    /**
     * 检查是否所有玩家都已准备
     */
    private _checkAllPlayersReady(): boolean {
        return this._players.every(p => p.isReady);
    }
    
    /**
     * 延迟执行函数
     */
    private _scheduleFunction(callback: Function, delay: number) {
        this.scheduleOnce(() => {
            callback();
        }, delay);
    }
    
    /**
     * 模拟其他玩家状态变化（用于测试）
     */
    public simulateOtherPlayerReady(playerId: string) {
        const player = this._players.find(p => p.id === playerId);
        if (player && player.id !== this._currentPlayerId) {
            player.isReady = !player.isReady;
            this._updateUI();
            log(`模拟：玩家 ${player.name} ${player.isReady ? '已准备' : '取消准备'}`);
        }
    }
    
    /**
     * 获取当前玩家列表（用于调试）
     */
    public getPlayerList() {
        return this._players;
    }
    
    /**
     * 重置所有玩家状态（用于测试）
     */
    public resetAllPlayers() {
        this._players.forEach(p => p.isReady = false);
        this._isCurrentPlayerReady = false;
        this._updateUI();
        log('已重置所有玩家状态');
    }
}
