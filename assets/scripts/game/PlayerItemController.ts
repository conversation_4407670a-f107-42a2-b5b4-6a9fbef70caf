import { _decorator, Component, Node, Label, Sprite, SpriteFrame, Color } from 'cc';
import { PlayerInfo, PlayerStatus } from '../bean/PlayerInfo';

const { ccclass, property } = _decorator;

/**
 * 玩家条目控制器
 * 用于管理单个玩家条目的显示和状态更新
 */
@ccclass('PlayerItemController')
export class PlayerItemController extends Component {
    
    @property(Node)
    avatarNode: Node = null!;
    
    @property(Label)
    playerNameLabel: Label = null!;
    
    @property(Node)
    readyIconNode: Node = null!;
    
    @property(Label)
    readyStatusLabel: Label = null!;
    
    @property(SpriteFrame)
    readyIconSprite: SpriteFrame = null!;
    
    @property(SpriteFrame)
    notReadyIconSprite: SpriteFrame = null!;
    
    @property(SpriteFrame)
    joiningIconSprite: SpriteFrame = null!;
    
    private _playerInfo: PlayerInfo | null = null;
    
    onLoad() {
        // 初始化组件引用
        this._initComponents();
    }
    
    /**
     * 初始化组件引用
     */
    private _initComponents() {
        if (!this.avatarNode) {
            this.avatarNode = this.node.getChildByName('Avatar')!;
        }
        
        if (!this.playerNameLabel) {
            const nameNode = this.node.getChildByName('PlayerName')!;
            this.playerNameLabel = nameNode.getComponent(Label)!;
        }
        
        if (!this.readyIconNode) {
            this.readyIconNode = this.node.getChildByName('ReadyIcon')!;
        }
        
        if (!this.readyStatusLabel) {
            const statusNode = this.node.getChildByName('ReadyStatus')!;
            this.readyStatusLabel = statusNode.getComponent(Label)!;
        }
    }
    
    /**
     * 更新玩家信息显示
     * @param playerInfo 玩家信息
     */
    public updatePlayerInfo(playerInfo: PlayerInfo) {
        this._playerInfo = playerInfo;
        
        // 更新玩家昵称
        this.playerNameLabel.string = playerInfo.name;
        
        // 更新准备状态
        this._updateReadyStatus(playerInfo.isReady);
        
        // 更新头像（如果有的话）
        this._updateAvatar(playerInfo.avatar);
        
        // 如果是当前玩家，可以添加特殊标识
        if (playerInfo.isCurrentPlayer) {
            this.playerNameLabel.color = new Color(255, 215, 0); // 金色
        } else {
            this.playerNameLabel.color = Color.WHITE;
        }
    }
    
    /**
     * 更新准备状态显示
     * @param isReady 是否已准备
     */
    private _updateReadyStatus(isReady: boolean) {
        const iconSprite = this.readyIconNode.getComponent(Sprite);
        
        if (isReady) {
            // 已准备状态
            this.readyStatusLabel.string = "已准备";
            this.readyStatusLabel.color = new Color(0, 255, 0); // 绿色
            
            if (iconSprite && this.readyIconSprite) {
                iconSprite.spriteFrame = this.readyIconSprite;
            }
        } else {
            // 未准备状态
            this.readyStatusLabel.string = "未准备";
            this.readyStatusLabel.color = new Color(128, 128, 128); // 灰色
            
            if (iconSprite && this.notReadyIconSprite) {
                iconSprite.spriteFrame = this.notReadyIconSprite;
            }
        }
    }
    
    /**
     * 更新头像显示
     * @param avatarPath 头像资源路径
     */
    private _updateAvatar(avatarPath?: string) {
        if (!avatarPath || !this.avatarNode) {
            return;
        }
        
        const avatarSprite = this.avatarNode.getComponent(Sprite);
        if (avatarSprite) {
            // 这里可以加载头像资源
            // 暂时使用默认头像
        }
    }
    
    /**
     * 设置加入中状态
     */
    public setJoiningStatus() {
        this.readyStatusLabel.string = "加入中...";
        this.readyStatusLabel.color = new Color(255, 255, 0); // 黄色
        
        const iconSprite = this.readyIconNode.getComponent(Sprite);
        if (iconSprite && this.joiningIconSprite) {
            iconSprite.spriteFrame = this.joiningIconSprite;
        }
    }
    
    /**
     * 获取当前玩家信息
     */
    public getPlayerInfo(): PlayerInfo | null {
        return this._playerInfo;
    }
}
