import { _decorator, Component, Node, Label, <PERSON><PERSON>, Color, director } from 'cc';
import { PlayerInfo } from '../bean/PlayerInfo';

const { ccclass, property } = _decorator;

/**
 * 等待房间测试脚本
 * 简化版本，用于演示基本功能
 */
@ccclass('WaitingRoomTest')
export class WaitingRoomTest extends Component {
    
    @property(Button)
    backButton: Button = null!;
    
    @property(Label)
    titleLabel: Label = null!;
    
    @property(Label)
    statusLabel: Label = null!;
    
    @property(Button)
    readyButton: Button = null!;
    
    @property(Label)
    readyButtonLabel: Label = null!;
    
    // 模拟数据
    private _players: PlayerInfo[] = [];
    private _currentPlayerId: string = "player1";
    private _isCurrentPlayerReady: boolean = false;
    
    onLoad() {
        this._initComponents();
        this._initEvents();
        this._initMockData();
    }
    
    start() {
        this._updateUI();
    }
    
    /**
     * 初始化组件引用
     */
    private _initComponents() {
        // 自动查找组件
        if (!this.backButton) {
            const backNode = this.node.getChildByPath('TopPanel/BackButton');
            if (backNode) {
                this.backButton = backNode.getComponent(Button)!;
            }
        }
        
        if (!this.titleLabel) {
            const titleNode = this.node.getChildByPath('TopPanel/TitleLabel');
            if (titleNode) {
                this.titleLabel = titleNode.getComponent(Label)!;
            }
        }
        
        if (!this.statusLabel) {
            const statusNode = this.node.getChildByName('StatusLabel');
            if (statusNode) {
                this.statusLabel = statusNode.getComponent(Label)!;
            }
        }
        
        if (!this.readyButton) {
            const readyNode = this.node.getChildByName('ReadyButton');
            if (readyNode) {
                this.readyButton = readyNode.getComponent(Button)!;
            }
        }
        
        if (!this.readyButtonLabel) {
            const labelNode = this.node.getChildByPath('ReadyButton/Label');
            if (labelNode) {
                this.readyButtonLabel = labelNode.getComponent(Label)!;
            }
        }
    }
    
    /**
     * 初始化事件监听
     */
    private _initEvents() {
        if (this.backButton) {
            this.backButton.node.on(Button.EventType.CLICK, this.onBackButtonClick, this);
        }
        
        if (this.readyButton) {
            this.readyButton.node.on(Button.EventType.CLICK, this.onReadyButtonClick, this);
        }
    }
    
    /**
     * 初始化模拟数据
     */
    private _initMockData() {
        this._players = [
            {
                id: "player1",
                name: "师家 A",
                isReady: false,
                isCurrentPlayer: true
            },
            {
                id: "player2", 
                name: "师家 B",
                isReady: false
            },
            {
                id: "player3",
                name: "师家 C", 
                isReady: true
            },
            {
                id: "player4",
                name: "师家 D",
                isReady: false
            }
        ];
    }
    
    /**
     * 更新UI显示
     */
    private _updateUI() {
        // 更新标题
        if (this.titleLabel) {
            this.titleLabel.string = "等待房间";
        }
        
        // 更新状态文本
        this._updateStatusText();
        
        // 更新准备按钮
        this._updateReadyButton();
    }
    
    /**
     * 更新状态文本
     */
    private _updateStatusText() {
        if (!this.statusLabel) return;
        
        const readyCount = this._players.filter(p => p.isReady).length;
        const totalCount = this._players.length;
        
        this.statusLabel.string = `等待所有玩家准备 (${readyCount}/${totalCount})`;
    }
    
    /**
     * 更新准备按钮
     */
    private _updateReadyButton() {
        if (!this.readyButton || !this.readyButtonLabel) return;
        
        if (this._isCurrentPlayerReady) {
            this.readyButtonLabel.string = "取消准备";
            this.readyButtonLabel.color = new Color(255, 100, 100); // 红色
        } else {
            this.readyButtonLabel.string = "准备";
            this.readyButtonLabel.color = new Color(100, 255, 100); // 绿色
        }
    }
    
    /**
     * 准备按钮点击事件
     */
    public onReadyButtonClick() {
        this._isCurrentPlayerReady = !this._isCurrentPlayerReady;
        
        // 更新当前玩家状态
        const currentPlayer = this._players.find(p => p.id === this._currentPlayerId);
        if (currentPlayer) {
            currentPlayer.isReady = this._isCurrentPlayerReady;
        }
        
        // 更新UI
        this._updateUI();
        
        console.log(`玩家 ${this._currentPlayerId} ${this._isCurrentPlayerReady ? '已准备' : '取消准备'}`);
        
        // 检查是否所有玩家都已准备
        if (this._checkAllPlayersReady()) {
            console.log('所有玩家都已准备，可以开始游戏！');
        }
    }
    
    /**
     * 返回按钮点击事件
     */
    public onBackButtonClick() {
        console.log('返回上一个场景');
        // 这里可以加载上一个场景
        // director.loadScene('MainMenu');
    }
    
    /**
     * 检查是否所有玩家都已准备
     */
    private _checkAllPlayersReady(): boolean {
        return this._players.every(p => p.isReady);
    }
    
    /**
     * 模拟其他玩家状态变化
     */
    public simulateOtherPlayerReady(playerId: string) {
        const player = this._players.find(p => p.id === playerId);
        if (player && player.id !== this._currentPlayerId) {
            player.isReady = !player.isReady;
            this._updateUI();
            console.log(`玩家 ${player.name} ${player.isReady ? '已准备' : '取消准备'}`);
        }
    }
}
