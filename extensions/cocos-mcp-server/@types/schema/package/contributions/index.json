{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "description": "其他扩展插件的扩展配置 / Extended configuration for other extension plugins", "properties": {"menu": {"type": "array", "description": "菜单配置 / Menu configuration", "items": {"type": "object", "properties": {"path": {"type": "string", "description": "菜单路径 / Menu path"}, "label": {"type": "string", "description": "菜单标签 / Menu label"}, "message": {"type": "string", "description": "菜单消息 / Menu message"}}}}, "messages": {"type": "object", "description": "消息配置 / Message configuration", "additionalProperties": {"type": "object", "properties": {"methods": {"type": "array", "items": {"type": "string"}}}}}, "panels": {"$ref": "../base/panels.json"}}, "required": []}