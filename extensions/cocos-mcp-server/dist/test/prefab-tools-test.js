"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrefabToolsTest = void 0;
const prefab_tools_1 = require("../tools/prefab-tools");
// 预制体工具测试
class PrefabToolsTest {
    constructor() {
        this.prefabTools = new prefab_tools_1.PrefabTools();
    }
    async runAllTests() {
        console.log('开始预制体工具测试...');
        try {
            // 测试1: 获取工具列表
            await this.testGetTools();
            // 测试2: 获取预制体列表
            await this.testGetPrefabList();
            // 测试3: 测试预制体创建（模拟）
            await this.testCreatePrefab();
            // 测试3.5: 测试预制体实例化（模拟）
            await this.testInstantiatePrefab();
            // 测试4: 测试预制体验证
            await this.testValidatePrefab();
            console.log('所有测试完成！');
        }
        catch (error) {
            console.error('测试过程中发生错误:', error);
        }
    }
    async testGetTools() {
        console.log('测试1: 获取工具列表');
        const tools = this.prefabTools.getTools();
        console.log(`找到 ${tools.length} 个工具:`);
        tools.forEach(tool => {
            console.log(`  - ${tool.name}: ${tool.description}`);
        });
        console.log('测试1完成\n');
    }
    async testGetPrefabList() {
        var _a;
        console.log('测试2: 获取预制体列表');
        try {
            const result = await this.prefabTools.execute('get_prefab_list', { folder: 'db://assets' });
            if (result.success) {
                console.log(`找到 ${((_a = result.data) === null || _a === void 0 ? void 0 : _a.length) || 0} 个预制体`);
                if (result.data && result.data.length > 0) {
                    result.data.slice(0, 3).forEach((prefab) => {
                        console.log(`  - ${prefab.name}: ${prefab.path}`);
                    });
                }
            }
            else {
                console.log('获取预制体列表失败:', result.error);
            }
        }
        catch (error) {
            console.log('获取预制体列表时发生错误:', error);
        }
        console.log('测试2完成\n');
    }
    async testCreatePrefab() {
        console.log('测试3: 测试预制体创建（模拟）');
        try {
            // 模拟创建预制体
            const mockArgs = {
                nodeUuid: 'mock-node-uuid',
                savePath: 'db://assets/test',
                prefabName: 'TestPrefab'
            };
            const result = await this.prefabTools.execute('create_prefab', mockArgs);
            console.log('创建预制体结果:', result);
        }
        catch (error) {
            console.log('创建预制体时发生错误:', error);
        }
        console.log('测试3完成\n');
    }
    async testInstantiatePrefab() {
        console.log('测试3.5: 测试预制体实例化（模拟）');
        try {
            // 模拟实例化预制体
            const mockArgs = {
                prefabPath: 'db://assets/prefabs/TestPrefab.prefab',
                parentUuid: 'canvas-uuid',
                position: { x: 100, y: 200, z: 0 }
            };
            const result = await this.prefabTools.execute('instantiate_prefab', mockArgs);
            console.log('实例化预制体结果:', result);
            // 测试API参数构建
            this.testCreateNodeAPIParams();
        }
        catch (error) {
            console.log('实例化预制体时发生错误:', error);
        }
        console.log('测试3.5完成\n');
    }
    testCreateNodeAPIParams() {
        console.log('测试 create-node API 参数构建...');
        // 模拟 assetUuid
        const assetUuid = 'mock-prefab-uuid';
        // 测试基本参数
        const basicOptions = {
            assetUuid: assetUuid,
            name: 'TestPrefabInstance'
        };
        console.log('基本参数:', basicOptions);
        // 测试带父节点的参数
        const withParentOptions = Object.assign(Object.assign({}, basicOptions), { parent: 'parent-node-uuid' });
        console.log('带父节点参数:', withParentOptions);
        // 测试带位置的参数
        const withPositionOptions = Object.assign(Object.assign({}, basicOptions), { dump: {
                position: { x: 100, y: 200, z: 0 }
            } });
        console.log('带位置参数:', withPositionOptions);
        // 测试完整参数
        const fullOptions = {
            assetUuid: assetUuid,
            name: 'TestPrefabInstance',
            parent: 'parent-node-uuid',
            dump: {
                position: { x: 100, y: 200, z: 0 }
            },
            keepWorldTransform: false,
            unlinkPrefab: false
        };
        console.log('完整参数:', fullOptions);
    }
    async testValidatePrefab() {
        console.log('测试4: 测试预制体验证');
        try {
            // 测试验证一个不存在的预制体
            const result = await this.prefabTools.execute('validate_prefab', {
                prefabPath: 'db://assets/nonexistent.prefab'
            });
            console.log('验证预制体结果:', result);
        }
        catch (error) {
            console.log('验证预制体时发生错误:', error);
        }
        console.log('测试4完成\n');
    }
    // 测试预制体数据结构生成
    testPrefabDataGeneration() {
        console.log('测试预制体数据结构生成...');
        const mockNodeData = {
            name: 'TestNode',
            position: { x: 0, y: 0, z: 0 },
            scale: { x: 1, y: 1, z: 1 },
            active: true,
            children: [],
            components: [
                {
                    type: 'cc.UITransform',
                    enabled: true,
                    properties: {
                        _contentSize: { width: 100, height: 100 },
                        _anchorPoint: { x: 0.5, y: 0.5 }
                    }
                }
            ]
        };
        const prefabUuid = this.prefabTools['generateUUID']();
        const prefabData = this.prefabTools['createPrefabData'](mockNodeData, 'TestPrefab', prefabUuid);
        console.log('生成的预制体数据结构:');
        console.log(JSON.stringify(prefabData, null, 2));
        // 验证数据结构
        const validationResult = this.prefabTools['validatePrefabFormat'](prefabData);
        console.log('验证结果:', validationResult);
        console.log('预制体数据结构生成测试完成\n');
    }
    // 测试UUID生成
    testUUIDGeneration() {
        console.log('测试UUID生成...');
        const uuids = [];
        for (let i = 0; i < 5; i++) {
            const uuid = this.prefabTools['generateUUID']();
            uuids.push(uuid);
            console.log(`UUID ${i + 1}: ${uuid}`);
        }
        // 检查UUID格式
        const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        const validUuids = uuids.filter(uuid => uuidPattern.test(uuid));
        console.log(`UUID格式验证: ${validUuids.length}/${uuids.length} 个有效`);
        console.log('UUID生成测试完成\n');
    }
}
exports.PrefabToolsTest = PrefabToolsTest;
// 如果直接运行此文件
if (typeof module !== 'undefined' && module.exports) {
    const test = new PrefabToolsTest();
    test.runAllTests();
    test.testPrefabDataGeneration();
    test.testUUIDGeneration();
}
//# sourceMappingURL=data:application/json;base64,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